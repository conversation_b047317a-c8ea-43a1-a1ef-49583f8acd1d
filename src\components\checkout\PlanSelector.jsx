'use client'

import { Check } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Badge, Card, Label, RadioGroup, RadioGroupItem, Text } from '@components'
import { useSubscription } from '@hooks'
import { cn } from '@utils'

export const PlanSelector = () => {
  const t = useTranslations('CheckoutPage')
  const { subscriptionState, updateSubscriptionState } = useSubscription()
  const { selectedPlan, products, currencySymbolMap, currency } = subscriptionState

  const setSelectedPlan = (plan) => {
    const selectedProduct = products.find((product) => product.id === plan)
    updateSubscriptionState({
      type: 'updateState',
      payload: {
        selectedPlan: selectedProduct,
        currency: currency
      }
    })
  }

  return (
    <RadioGroup
      value={selectedPlan?.id}
      onValueChange={setSelectedPlan}
      className='flex gap-4 lg:mb-6'>
      {products.map((plan) => {
        const finalPrice = plan?.trialpriceJSON || plan.priceJSON
        const priceData = JSON.parse(finalPrice)['data'][0]
        const currencyOptions = priceData.currency_options
        const price = currencyOptions[currency]
        const trialPrice = plan?.trialpriceJSON ? true : false
        const priceString = `${currencySymbolMap[currency]}${price.unit_amount / 100}`

        return (
          <div key={plan.id} className='w-full'>
            <Card
              className={cn(
                'w-full p-2.5 rounded-lg cursor-pointer shadow-none border-2',
                selectedPlan?.id === plan.id
                  ? 'border-blue-600 shadow-md'
                  : 'border-slate-200 bg-white'
              )}>
              <Label htmlFor={plan.id} className='cursor-pointer'>
                <div className='flex flex-col items-start gap-2'>
                  <div className='relative'>
                    <RadioGroupItem value={plan.id} id={plan.id} className='sr-only' />
                    <div
                      className={cn(
                        'w-6 h-6 rounded-full border-2 flex items-center justify-center lg:mb-0 mb-2',
                        selectedPlan?.id === plan.id
                          ? 'bg-blue-600 border-blue-600'
                          : 'border-gray-300 bg-white'
                      )}>
                      {selectedPlan?.id === plan.id && (
                        <Check className='w-4 h-4 text-white' />
                      )}
                    </div>
                  </div>
                  <div className='flex flex-col gap-1'>
                    <div className='flex items-center gap-2'>
                      <Text as='h4' weight='medium' className='text-slate-700'>
                        {trialPrice ? t('7day') : plan.title}
                      </Text>
                      {plan?.isPopular && (
                        <Badge
                          variant='default'
                          className='bg-slate-700 rounded-full h-5 px-1.5'>
                          {t('popular')}
                        </Badge>
                      )}
                    </div>
                    <div className='flex items-center gap-1.5'>
                      <Text
                        as='h4'
                        variant='base'
                        weight='semibold'
                        className='text-slate-700'>
                        {priceString}
                      </Text>
                      <Text variant='xs' weight='medium' className='text-slate-500'>
                        {plan?.description}
                      </Text>
                    </div>
                  </div>
                </div>
              </Label>
            </Card>
          </div>
        )
      })}
    </RadioGroup>
  )
}
