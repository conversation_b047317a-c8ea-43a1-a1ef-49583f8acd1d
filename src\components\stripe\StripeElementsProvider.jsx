'use client'

import { useMemo } from 'react'
import { useSubscription } from '@hooks'
import { Elements } from '@stripe/react-stripe-js'
import { getStripe } from '@utils/getStripejs'

const fonts = [
  {
    cssSrc: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap'
  }
]

const appearance = {
  theme: 'stripe',
  variables: {
    borderRadius: '8px',
    focusBoxShadow: 'none',
    focusOutline: '#2563eb auto 0px',
    fontFamily: '"Inter", sans-serif',
    colorPrimary: '#3B82F6',
    colorText: '#334155'
  },
  rules: {
    '.Block': {
      backgroundColor: 'transparent',
      boxShadow: 'none',
      padding: '12px'
    },
    '.Label': {
      color: '#475569',
      fontSize: '12px',
      fontWeight: '500',
      marginBottom: '6px',
      lineHeight: '16px !important'
    },
    '.Input': {
      color: '#334155',
      padding: '8.5px 10px',
      fontSize: '16px',
      lineHeight: '20px',
      borderRadius: '8px',
      boxShadow: '0px 1px 2px 0px rgba(0, 0, 0, 0.04)'
    },
    '.Tab': {
      padding: '12px 16px',
      backgroundColor: 'transparent',
      boxShadow: 'none'
    },
    '.TabLabel': {
      paddingTop: '8px',
      fontSize: '14px',
      fontWeight: '500',
      lineHeight: '20px'
    }
  }
}

const stripeOptions = {
  mode: 'subscription',
  amount: 0,
  currency: 'usd',
  appearance: appearance,
  fonts: fonts
}

export function StripeElementsProvider({
  children,
  subscriptionOptions = {},
  locale = 'auto'
}) {
  const { subscriptionState } = useSubscription()
  const { selectedPlan, currency } = subscriptionState

  const options = useMemo(() => {
    if (!selectedPlan) return stripeOptions

    const finalPrice = selectedPlan?.trialpriceJSON || selectedPlan.priceJSON

    const priceData = JSON.parse(finalPrice)['data'][0]
    const currencyOptions = priceData.currency_options
    const price = currencyOptions[currency]
    return {
      ...stripeOptions,
      amount: price.unit_amount,
      currency: currency,
      locale
    }
  }, [selectedPlan, currency, locale])

  return (
    <Elements stripe={getStripe()} options={{ ...options, ...subscriptionOptions }}>
      {children}
    </Elements>
  )
}
